# -*- coding: utf-8 -*-
"""
出院记录规则质控主控制文件
"""
from datetime import datetime, timedelta
import re

def check_timeliness(medical_record):
    """检查时效性：出院记录是否在出院后24小时内完成"""
    try:
        discharge_time = medical_record.get('discharge_time')
        record_time = medical_record.get('record_completion_time')
        
        if not discharge_time or not record_time:
            return True  # 缺少时间信息视为有问题
        
        # 简化时间检查逻辑
        return False  # 暂时返回无问题
    except:
        return True

def check_section_completeness(medical_record):
    """检查段落完整性"""
    content = medical_record.get('content', '')
    required_sections = ['诊疗经过', '入院诊断', '入院情况', '出院诊断', '出院情况', '出院日期', '入院日期']
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    return len(missing_sections) > 0

def check_signature_compliance(medical_record):
    """检查签名合理性"""
    content = medical_record.get('content', '')
    signatures = medical_record.get('signatures', [])
    
    required_signatures = ['住院医师', '主治医师']
    for sig_type in required_signatures:
        if sig_type not in str(signatures):
            return True
    
    return False

def run_regulatory_quality_control(medical_record):
    """运行所有规则质控检查"""
    results = {}
    
    # 时效性检查
    results['缺出院记录，或未在出院后24小时内完成'] = {
        'rule_type': '时效性',
        'classification': '手术科室',
        'deduction_points': 60.0,
        'has_problem': check_timeliness(medical_record),
        'type': 'regulatory'
    }
    
    # 段落完整性检查
    section_rules = [
        ('缺诊疗经过', 6.0),
        ('缺入院诊断', 6.0),
        ('缺入院情况', 6.0),
        ('缺出院诊断', 6.0),
        ('缺出院情况', 6.0),
        ('缺出院日期', 6.0),
        ('缺入院日期', 6.0)
    ]
    
    for rule_content, points in section_rules:
        results[rule_content] = {
            'rule_type': '段落完整性',
            'classification': '手术科室',
            'deduction_points': points,
            'has_problem': check_section_completeness(medical_record),
            'type': 'regulatory'
        }
    
    # 签名合理性检查
    results['缺住院医师、主治医师或以上签名'] = {
        'rule_type': '签名合理性',
        'classification': '手术科室',
        'deduction_points': 3.0,
        'has_problem': check_signature_compliance(medical_record),
        'type': 'regulatory'
    }
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试出院记录内容",
        "discharge_time": "2024-01-10 10:00:00",
        "record_completion_time": "2024-01-10 15:00:00",
        "signatures": ["住院医师: 张医生", "主治医师: 李医生"]
    }
    
    results = run_regulatory_quality_control(test_record)
    print("出院记录规则质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: {'有问题' if result['has_problem'] else '无问题'}")
