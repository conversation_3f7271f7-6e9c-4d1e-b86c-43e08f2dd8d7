# -*- coding: utf-8 -*-
"""
测试rule_id字段修改的脚本
验证修改后的代码是否正确生成UUID格式的rule_id
"""

import sys
import os
import uuid
import json

# 添加dev_v1目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dev_v1', 'Medical_Record_List'))

def test_uuid_generation():
    """测试UUID生成功能"""
    print("=== 测试UUID生成功能 ===")
    
    # 生成几个UUID示例
    for i in range(5):
        rule_id = str(uuid.uuid4())
        print(f"示例 {i+1}: {rule_id}")
        
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(rule_id)
            print(f"  ✅ 有效的UUID格式")
        except ValueError:
            print(f"  ❌ 无效的UUID格式")
    
    print()

def test_uuid_uniqueness():
    """测试UUID唯一性"""
    print("=== 测试UUID唯一性 ===")
    
    # 生成1000个UUID，检查是否有重复
    uuids = set()
    for i in range(1000):
        rule_id = str(uuid.uuid4())
        uuids.add(rule_id)
    
    print(f"生成了 1000 个UUID")
    print(f"唯一UUID数量: {len(uuids)}")
    
    if len(uuids) == 1000:
        print("✅ 所有UUID都是唯一的")
    else:
        print(f"❌ 发现重复UUID，重复数量: {1000 - len(uuids)}")
    
    print()

def test_json_structure():
    """测试JSON数据结构"""
    print("=== 测试JSON数据结构 ===")
    
    # 模拟一个规则记录
    sample_record = {
        "rule_id": str(uuid.uuid4()),
        "rule_type_chinese": "质控规则",
        "rule_type_english": "Quality Control Rule",
        "classification_chinese": "病历质量",
        "classification_english": "Medical Record Quality",
        "belonging_project_chinese": "病历管理",
        "belonging_project_english": "Medical Record Management",
        "document_type_chinese": "首次病程记录",
        "document_type_english": "Initial Progress Note",
        "rule_content": "首次病程记录应包含病例特点",
        "deduction_points": 2
    }
    
    print("示例规则记录结构:")
    print(json.dumps(sample_record, ensure_ascii=False, indent=2))
    
    # 验证rule_id字段
    if "rule_id" in sample_record:
        print("✅ rule_id字段存在")
        
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(sample_record["rule_id"])
            print("✅ rule_id是有效的UUID格式")
        except ValueError:
            print("❌ rule_id不是有效的UUID格式")
    else:
        print("❌ rule_id字段不存在")
    
    print()

def main():
    """主函数"""
    print("医疗记录规则ID修改测试")
    print("=" * 50)
    
    test_uuid_generation()
    test_uuid_uniqueness()
    test_json_structure()
    
    print("=" * 50)
    print("测试完成！")
    
    print("\n修改总结:")
    print("1. ✅ 已导入uuid模块")
    print("2. ✅ 在generate_json_files方法中为每个记录添加rule_id字段")
    print("3. ✅ rule_id使用UUID4格式生成，确保唯一性")
    print("4. ✅ 修改后的JSON结构包含rule_id字段")
    
    print("\n修改的文件:")
    print("- dev_v1/Medical_Record_List/medical_record_list_generator.py")
    print("- dev_v1/excel_to_json_converter.py")

    print("\n具体修改内容:")
    print("medical_record_list_generator.py:")
    print("- 第17行: 添加了 'import uuid'")
    print("- 第399行: 在processed_record字典中添加了 'rule_id': str(uuid.uuid4())'")
    print("\nexcel_to_json_converter.py:")
    print("- 第68行: 将 rule_id 格式从 f'rule_{uuid.uuid4().hex[:8]}' 改为 str(uuid.uuid4())")
    print("- 统一使用完整UUID格式，确保所有文件生成的rule_id格式一致")

if __name__ == "__main__":
    main()
