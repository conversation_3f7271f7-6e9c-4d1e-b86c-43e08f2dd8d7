# -*- coding: utf-8 -*-
"""
质控系统总控制文件
根据文档类型调用相应的质控控制器
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

class QualityControlMain:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.json_dir = self.base_dir.parent / "rule_type" / "rule_type_json"
        
        # 文档类型映射表
        self.document_type_mapping = {}
        self._load_document_mappings()
        
        # 控制器映射
        self.controllers = {}
        self._load_controllers()
    
    def _load_document_mappings(self):
        """加载文档类型映射"""
        try:
            for json_file in self.json_dir.glob("*.json"):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    chinese_name = data['metadata']['document_type_chinese']
                    english_name = data['metadata']['document_type_english']
                    self.document_type_mapping[chinese_name] = english_name
                    print(f"加载文档类型映射: {chinese_name} -> {english_name}")
        except Exception as e:
            print(f"加载文档类型映射失败: {e}")
    
    def _load_controllers(self):
        """动态加载控制器"""
        for chinese_name, english_name in self.document_type_mapping.items():
            try:
                controller_name = f"{english_name.lower().replace(' ', '_')}_controller"
                controller_module = __import__(controller_name, fromlist=[controller_name])
                self.controllers[chinese_name] = controller_module
                print(f"加载控制器: {chinese_name} -> {controller_name}")
            except ImportError as e:
                print(f"无法加载控制器 {controller_name}: {e}")
    
    def run_quality_control(self, document_type_chinese, medical_record):
        """
        运行质控检查
        
        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据
            
        Returns:
            dict: 综合质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }
        
        english_name = self.document_type_mapping[document_type_chinese]
        
        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }
        
        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_integrated_quality_control(medical_record)
            
            # 添加元数据
            result["metadata"] = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "total_rules": len(result.get("regulatory_results", {})) + len(result.get("connotation_results", {})),
                "regulatory_count": len(result.get("regulatory_results", {})),
                "connotation_count": len(result.get("connotation_results", {}))
            }
            
            return result
            
        except Exception as e:
            return {
                "error": f"质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }
    
    def get_supported_document_types(self):
        """获取支持的文档类型列表"""
        return {
            "chinese_types": list(self.document_type_mapping.keys()),
            "english_types": list(self.document_type_mapping.values()),
            "mappings": self.document_type_mapping
        }
    
    def get_quality_control_summary(self, results):
        """
        生成质控结果摘要
        
        Args:
            results (dict): 质控结果
            
        Returns:
            dict: 质控摘要
        """
        if "error" in results:
            return {"error": results["error"]}
        
        summary = {
            "document_info": results.get("metadata", {}),
            "overall_score": 0,
            "total_deductions": 0,
            "problem_count": 0,
            "regulatory_summary": {},
            "connotation_summary": {},
            "recommendations": []
        }
        
        # 统计规则质控结果
        regulatory_results = results.get("regulatory_results", {})
        regulatory_problems = 0
        regulatory_deductions = 0
        
        for rule, result in regulatory_results.items():
            if result.get("has_problem", False):
                regulatory_problems += 1
                regulatory_deductions += result.get("deduction_points", 0)
        
        summary["regulatory_summary"] = {
            "total_rules": len(regulatory_results),
            "problem_count": regulatory_problems,
            "total_deductions": regulatory_deductions
        }
        
        # 统计内涵质控结果
        connotation_results = results.get("connotation_results", {})
        connotation_total_score = 0
        connotation_max_score = 0
        
        for rule, result in connotation_results.items():
            connotation_total_score += result.get("score", 0)
            connotation_max_score += result.get("max_points", 0)
            if result.get("problems"):
                summary["recommendations"].append(result.get("suggestions", ""))
        
        summary["connotation_summary"] = {
            "total_rules": len(connotation_results),
            "total_score": connotation_total_score,
            "max_score": connotation_max_score,
            "score_rate": connotation_total_score / connotation_max_score if connotation_max_score > 0 else 0
        }
        
        # 计算总体评分
        summary["total_deductions"] = regulatory_deductions
        summary["problem_count"] = regulatory_problems + len([r for r in connotation_results.values() if r.get("problems")])
        
        return summary

def main():
    """主函数示例"""
    qc_main = QualityControlMain()
    
    # 显示支持的文档类型
    supported_types = qc_main.get_supported_document_types()
    print("支持的文档类型:")
    for chinese, english in supported_types["mappings"].items():
        print(f"  {chinese} ({english})")
    
    # 测试示例
    test_record = {
        "patient_id": "12345",
        "patient_name": "张三",
        "content": "这是一个测试病历内容...",
        "admission_date": "2024-01-01",
        "discharge_date": "2024-01-10",
        "diagnosis": {
            "admission": "高血压",
            "discharge": "高血压，已控制"
        },
        "treatment": {
            "medications": ["降压药"],
            "procedures": []
        }
    }
    
    # 运行质控
    for doc_type in supported_types["chinese_types"]:
        print(f"\n运行 {doc_type} 质控...")
        results = qc_main.run_quality_control(doc_type, test_record)
        
        if "error" in results:
            print(f"错误: {results['error']}")
        else:
            summary = qc_main.get_quality_control_summary(results)
            print(f"质控摘要: {summary}")

if __name__ == "__main__":
    main()
