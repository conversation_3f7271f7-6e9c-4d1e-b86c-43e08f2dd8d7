# -*- coding: utf-8 -*-
"""
质控系统测试脚本
演示完整的质控功能
"""
import json
from quality_control_main import QualityControlMain
from discharge_summary_controller import DischargeSummaryController
from initial_progress_note_controller import InitialProgressNoteController

def test_discharge_summary():
    """测试出院记录质控"""
    print("=" * 60)
    print("测试出院记录质控")
    print("=" * 60)
    
    # 创建测试病历数据
    test_record = {
        "patient_id": "P001",
        "patient_name": "张三",
        "content": """
        患者张三，男，45岁，因"胸痛3天"入院。
        入院诊断：急性心肌梗死
        诊疗经过：患者入院后给予抗凝、抗血小板聚集等治疗。
        出院诊断：急性心肌梗死（已控制）
        出院情况：患者症状明显改善，生命体征平稳。
        出院医嘱：阿司匹林 100mg 每日一次，氯吡格雷 75mg 每日一次，定期复查。
        """,
        "admission_date": "2024-01-01",
        "discharge_date": "2024-01-10",
        "discharge_time": "2024-01-10 10:00:00",
        "record_completion_time": "2024-01-10 15:00:00",
        "signatures": ["住院医师: 李医生", "主治医师: 王医生"]
    }
    
    # 使用控制器进行质控
    controller = DischargeSummaryController()
    report = controller.get_quality_control_report(test_record)
    
    print(f"患者信息: {report['patient_info']}")
    print(f"合规评分: {report['compliance_score']}")
    print(f"行动项目数量: {len(report['action_items'])}")
    
    # 显示详细结果
    results = report['quality_control_results']
    print(f"\n质控结果摘要:")
    print(f"- 总检查规则数: {results['summary']['total_rules_checked']}")
    print(f"- 规则质控问题: {results['summary']['regulatory_issues']}")
    print(f"- 内涵质控问题: {results['summary']['connotation_issues']}")
    print(f"- 总扣分: {results['summary']['total_deduction_points']}")
    print(f"- 总体状态: {results['summary']['overall_status']}")
    
    return report

def test_initial_progress_note():
    """测试首次病程记录质控"""
    print("\n" + "=" * 60)
    print("测试首次病程记录质控")
    print("=" * 60)
    
    # 创建测试病历数据
    test_record = {
        "patient_id": "P002",
        "patient_name": "李四",
        "content": """
        患者李四，女，38岁，因"发热、咳嗽5天"入院。
        病例特点：患者5天前无明显诱因出现发热，体温最高39.2℃，伴咳嗽、咳痰。
        初步诊断：社区获得性肺炎
        诊断依据：患者有发热、咳嗽症状，胸片示右下肺炎症。
        鉴别诊断：需与肺结核、肺癌等鉴别。
        诊疗计划：抗感染治疗，对症支持治疗，密切观察病情变化。
        """,
        "admission_time": "2024-01-01 08:00:00",
        "first_progress_time": "2024-01-01 12:00:00"
    }
    
    # 使用控制器进行质控
    controller = InitialProgressNoteController()
    report = controller.get_quality_control_report(test_record)
    
    print(f"患者信息: {report['patient_info']}")
    print(f"合规评分: {report['compliance_score']}")
    print(f"行动项目数量: {len(report['action_items'])}")
    
    # 显示详细结果
    results = report['quality_control_results']
    print(f"\n质控结果摘要:")
    print(f"- 总检查规则数: {results['summary']['total_rules_checked']}")
    print(f"- 规则质控问题: {results['summary']['regulatory_issues']}")
    print(f"- 内涵质控问题: {results['summary']['connotation_issues']}")
    print(f"- 总扣分: {results['summary']['total_deduction_points']}")
    print(f"- 总体状态: {results['summary']['overall_status']}")
    
    return report

def test_main_controller():
    """测试主控制器"""
    print("\n" + "=" * 60)
    print("测试主控制器")
    print("=" * 60)
    
    qc_main = QualityControlMain()
    
    # 显示支持的文档类型
    supported_types = qc_main.get_supported_document_types()
    print("支持的文档类型:")
    for chinese, english in supported_types["mappings"].items():
        print(f"  {chinese} ({english})")
    
    # 测试出院记录
    test_record = {
        "patient_id": "P003",
        "patient_name": "王五",
        "content": "简化的出院记录内容...",
        "discharge_time": "2024-01-10 10:00:00",
        "record_completion_time": "2024-01-10 15:00:00"
    }
    
    print(f"\n运行出院记录质控...")
    results = qc_main.run_quality_control("出院记录", test_record)
    
    if "error" not in results:
        summary = qc_main.get_quality_control_summary(results)
        print(f"质控摘要: {summary}")
    else:
        print(f"错误: {results['error']}")

def generate_quality_control_report():
    """生成完整的质控报告"""
    print("\n" + "=" * 60)
    print("生成完整质控报告")
    print("=" * 60)
    
    # 运行所有测试
    discharge_report = test_discharge_summary()
    initial_report = test_initial_progress_note()
    
    # 生成综合报告
    comprehensive_report = {
        "report_title": "医疗质控综合报告",
        "generated_time": "2024-01-15 10:00:00",
        "reports": {
            "discharge_summary": discharge_report,
            "initial_progress_note": initial_report
        },
        "summary": {
            "total_patients": 2,
            "total_documents": 2,
            "average_compliance_score": (
                discharge_report['compliance_score']['final_score'] + 
                initial_report['compliance_score']['final_score']
            ) / 2
        }
    }
    
    # 保存报告到文件
    with open("quality_control_comprehensive_report.json", 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n综合报告已保存到: quality_control_comprehensive_report.json")
    print(f"平均合规评分: {comprehensive_report['summary']['average_compliance_score']:.2f}")

def main():
    """主函数"""
    print("医疗质控系统测试")
    print("=" * 60)
    
    try:
        # 测试各个组件
        test_discharge_summary()
        test_initial_progress_note()
        test_main_controller()
        
        # 生成综合报告
        generate_quality_control_report()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
        print("\n质控系统功能总结:")
        print("✓ 规则质控：自动检查病历的完整性、时效性、签名等")
        print("✓ 内涵质控：使用大模型进行深度内容分析")
        print("✓ 控制层架构：支持多种文档类型的统一管理")
        print("✓ 结果整合：提供详细的质控报告和改进建议")
        print("✓ 评分系统：计算合规评分和等级评定")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
