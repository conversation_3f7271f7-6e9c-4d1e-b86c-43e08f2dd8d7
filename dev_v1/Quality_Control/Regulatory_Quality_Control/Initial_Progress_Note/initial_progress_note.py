# -*- coding: utf-8 -*-
"""
首次病程记录规则质控主控制文件
"""
from datetime import datetime, timedelta

def check_timeliness(medical_record):
    """检查时效性：首次病程是否在患者入院后8小时内完成"""
    try:
        admission_time = medical_record.get('admission_time')
        record_time = medical_record.get('first_progress_time')
        
        if not admission_time or not record_time:
            return True
        
        # 简化时间检查逻辑
        return False
    except:
        return True

def check_section_completeness(medical_record):
    """检查段落完整性"""
    content = medical_record.get('content', '')
    required_sections = ['病例特点', '初步诊断', '诊断依据', '鉴别诊断', '诊疗计划']
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    return len(missing_sections) > 0

def run_regulatory_quality_control(medical_record):
    """运行所有规则质控检查"""
    results = {}
    
    # 时效性检查
    results['首次病程未在患者入院后8小时内完成'] = {
        'rule_type': '时效性',
        'classification': '手术科室',
        'deduction_points': 60.0,
        'has_problem': check_timeliness(medical_record),
        'type': 'regulatory'
    }
    
    # 段落完整性检查
    section_rules = [
        ('首次病程缺病例特点', 6.0),
        ('首次病程缺初步诊断', 6.0),
        ('首次病程缺诊断依据', 6.0),
        ('首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断', 6.0),
        ('首次病程缺诊疗计划', 6.0)
    ]
    
    for rule_content, points in section_rules:
        results[rule_content] = {
            'rule_type': '段落完整性',
            'classification': '手术科室',
            'deduction_points': points,
            'has_problem': check_section_completeness(medical_record),
            'type': 'regulatory'
        }
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试首次病程记录内容",
        "admission_time": "2024-01-01 08:00:00",
        "first_progress_time": "2024-01-01 12:00:00"
    }
    
    results = run_regulatory_quality_control(test_record)
    print("首次病程记录规则质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: {'有问题' if result['has_problem'] else '无问题'}")
