

```python
# -*- coding: utf-8 -*-
"""
医疗质控检查模块 - 手术科室出院记录段落完整性检查
"""

from typing import Dict, Any, Union
import logging

# 配置日志记录
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def is_field_missing(data: Dict, field_path: str) -> bool:
    """
    检查指定路径的字段是否存在且非空
    
    Args:
        data: 病历数据字典
        field_path: 字段路径，用点号分隔的嵌套键（如 'section.subfield'）
        
    Returns:
        True表示字段缺失或为空，False表示字段存在且有内容
    """
    try:
        # 分割字段路径
        keys = field_path.split('.')
        current = data
        
        # 逐级查找字段
        for key in keys:
            if not isinstance(current, dict):
                return True  # 非字典结构视为异常
                
            if key not in current:
                return True  # 找不到键
                
            current = current[key]
            
        # 检查最终值是否为空
        if isinstance(current, (str, list, dict)):
            return not bool(current)  # 检查空字符串、空列表、空字典
        return False  # 非空结构
        
    except Exception as e:
        logger.error(f"字段检查异常: {str(e)}")
        return True

def check_section_completeness_001(data: Dict) -> bool:
    """
    检查出院记录中是否缺少出院情况字段
    
    质控规则：
    - 文档类型: 出院记录
    - 必填字段: discharge_summary.discharge_condition
    - 缺失扣分: 6.0分
    
    Args:
        data: 包含病历信息的字典数据
        
    Returns:
        True表示存在问题（字段缺失），False表示符合要求
    """
    # 检查文档类型是否匹配
    if not isinstance(data, dict):
        logger.warning("输入数据不是有效的字典结构")
        return True
        
    # 检查是否是出院记录类型
    if data.get('document_type') != 'Discharge Summary':
        return False  # 非目标文档类型不扣分
        
    # 检查关键字段是否存在
    if is_field_missing(data, 'discharge_summary.discharge_condition'):
        logger.info("发现缺失出院情况字段")
        return True  # 存在质量问题
        
    return False  # 符合要求

# ======================
# 使用示例
# ======================
if __name__ == "__main__":
    # 正常情况示例
    normal_case = {
        'document_type': 'Discharge Summary',
        'discharge_summary': {
            'discharge_condition': '患者一般情况良好，生命体征平稳，切口愈合良好，无发热等不适。'
        }
    }
    
    # 异常情况示例1: 缺失字段
    error_case_1 = {
        'document_type': 'Discharge Summary',
        'discharge_summary': {}  # 缺少discharge_condition
    }
    
    # 异常情况示例2: 空字符串
    error_case_2 = {
        'document_type': 'Discharge Summary',
        'discharge_summary': {
            'discharge_condition': ''  # 空字符串
        }
    }
    
    # 非目标文档类型示例
    other_type_case = {
        'document_type': 'Inpatient Record',
        'discharge_summary': {
            'discharge_condition': '正常'
        }
    }
    
    # 测试用例
    test_cases = [
        (normal_case, False),
        (error_case_1, True),
        (error_case_2, True),
        (other_type_case, False)
    ]
    
    for i, (test_data, expected) in enumerate(test_cases):
        result = check_section_completeness_001(test_data)
        print(f"\n测试用例 {i+1}:")
        print(f"输入数据: {test_data}")
        print(f"预期结果: {expected}")
        print(f"实际结果: {result}")
        assert result == expected, f"测试用例 {i+1} 失败"
    
    print("\n所有测试用例通过！")
```

### 代码说明：
1. **数据结构设计**：
   - 使用嵌套字典模拟病历数据结构
   - 通过字段路径字符串（如 'discharge_summary.discharge_condition'）定位嵌套字段

2. **异常处理**：
   - 处理非字典类型输入
   - 处理字段路径中任意层级的缺失
   - 处理空值、空字符串、空列表等边界情况

3. **日志记录**：
   - 记录警告和错误信息
   - 在发现质量问题时记录详细信息

4. **测试用例**：
   - 包含正常情况和各种异常情况
   - 验证非目标文档类型的正确处理

5. **扩展性**：
   - `is_field_missing` 函数可复用检查其他字段
   - 可通过修改字段路径快速适配其他质控规则

该实现能够准确检测出院记录中是否缺少出院情况字段，并根据文档类型进行条件判断，符合医疗质控要求。