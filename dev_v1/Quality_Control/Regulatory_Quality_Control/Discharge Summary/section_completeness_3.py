

```python
# -*- coding: utf-8 -*-
"""
医疗质控规则检查模块 - 手术科室出院记录入院诊断完整性检查
"""

def is_field_empty(record: dict, field_name: str) -> bool:
    """
    检查病历中的指定字段是否为空
    
    Args:
        record: 病历数据字典
        field_name: 要检查的字段名称
        
    Returns:
        bool: 如果字段不存在或为空返回True，否则返回False
    """
    # 参数类型检查
    if not isinstance(record, dict):
        return True  # 输入不是字典视为异常情况
    
    # 检查字段是否存在且非空
    value = record.get(field_name)
    if value is None:
        return True
    if isinstance(value, str) and value.strip() == '':
        return True
    return False

def check_section_completeness_001(record: dict) -> bool:
    """
    检查出院记录中是否缺少入院诊断字段
    
    质控规则：
    - 规则类型：段落完整性
    - 所属项目：出院(死亡)记录
    - 文档类型：出院记录
    - 扣分：6.0分
    
    Args:
        record: 包含病历信息的字典对象
        
    Returns:
        bool: True表示存在问题（缺少入院诊断），False表示符合要求
    """
    # 检查"入院诊断"字段是否存在且非空
    return is_field_empty(record, "入院诊断")

# ========================================
# 使用示例
# ========================================

if __name__ == "__main__":
    # 测试用例1：正常情况（包含入院诊断）
    normal_record = {
        "入院诊断": "急性阑尾炎",
        "手术名称": "阑尾切除术",
        "出院诊断": "急性阑尾炎术后"
    }
    
    # 测试用例2：缺少入院诊断字段
    missing_record = {
        "手术名称": "胆囊切除术",
        "出院诊断": "胆囊结石"
    }
    
    # 测试用例3：入院诊断为空字符串
    empty_record = {
        "入院诊断": "",
        "手术名称": "疝修补术",
        "出院诊断": "腹股沟疝"
    }
    
    # 测试用例4：入院诊断为空白字符
    whitespace_record = {
        "入院诊断": "   ",
        "手术名称": "胃切除术",
        "出院诊断": "胃癌术后"
    }
    
    # 执行测试
    print("测试用例1结果（应返回False）:", check_section_completeness_001(normal_record))
    print("测试用例2结果（应返回True）: ", check_section_completeness_001(missing_record))
    print("测试用例3结果（应返回True）: ", check_section_completeness_001(empty_record))
    print("测试用例4结果（应返回True）: ", check_section_completeness_001(whitespace_record))
```

### 代码说明：
1. **数据结构设计**：
   - 使用字典结构模拟病历数据，符合实际医疗信息系统的数据存储方式
   - 字段名称使用中文，符合国内医疗文档的命名习惯

2. **异常处理**：
   - 处理字段不存在的情况（KeyError）
   - 处理空字符串和空白字符的情况
   - 添加输入类型检查，防止非字典类型输入

3. **边界情况覆盖**：
   - 正常包含有效内容的字段
   - 完全缺失的字段
   - 空字符串
   - 仅包含空白字符的字符串

4. **可维护性**：
   - 将字段检查逻辑抽离为独立函数
   - 使用清晰的函数命名和参数命名
   - 添加详细的文档字符串

5. **测试用例**：
   - 包含4种典型情况的测试用例
   - 每个测试用例都有明确的预期结果说明

该实现能够准确检测出院记录中是否缺少入院诊断字段，满足医疗质控6分扣分规则的要求。