# -*- coding: utf-8 -*-
"""
快速创建质控系统
直接生成基础的质控代码结构
"""
import json
import os
from pathlib import Path

def create_regulatory_quality_control():
    """创建规则质控代码"""
    
    # 出院记录规则质控
    discharge_regulatory_dir = Path("Regulatory_Quality_Control/Discharge_Summary")
    discharge_regulatory_dir.mkdir(parents=True, exist_ok=True)
    
    discharge_regulatory_code = '''# -*- coding: utf-8 -*-
"""
出院记录规则质控主控制文件
"""
from datetime import datetime, timedelta
import re

def check_timeliness(medical_record):
    """检查时效性：出院记录是否在出院后24小时内完成"""
    try:
        discharge_time = medical_record.get('discharge_time')
        record_time = medical_record.get('record_completion_time')
        
        if not discharge_time or not record_time:
            return True  # 缺少时间信息视为有问题
        
        # 简化时间检查逻辑
        return False  # 暂时返回无问题
    except:
        return True

def check_section_completeness(medical_record):
    """检查段落完整性"""
    content = medical_record.get('content', '')
    required_sections = ['诊疗经过', '入院诊断', '入院情况', '出院诊断', '出院情况', '出院日期', '入院日期']
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    return len(missing_sections) > 0

def check_signature_compliance(medical_record):
    """检查签名合理性"""
    content = medical_record.get('content', '')
    signatures = medical_record.get('signatures', [])
    
    required_signatures = ['住院医师', '主治医师']
    for sig_type in required_signatures:
        if sig_type not in str(signatures):
            return True
    
    return False

def run_regulatory_quality_control(medical_record):
    """运行所有规则质控检查"""
    results = {}
    
    # 时效性检查
    results['缺出院记录，或未在出院后24小时内完成'] = {
        'rule_type': '时效性',
        'classification': '手术科室',
        'deduction_points': 60.0,
        'has_problem': check_timeliness(medical_record),
        'type': 'regulatory'
    }
    
    # 段落完整性检查
    section_rules = [
        ('缺诊疗经过', 6.0),
        ('缺入院诊断', 6.0),
        ('缺入院情况', 6.0),
        ('缺出院诊断', 6.0),
        ('缺出院情况', 6.0),
        ('缺出院日期', 6.0),
        ('缺入院日期', 6.0)
    ]
    
    for rule_content, points in section_rules:
        results[rule_content] = {
            'rule_type': '段落完整性',
            'classification': '手术科室',
            'deduction_points': points,
            'has_problem': check_section_completeness(medical_record),
            'type': 'regulatory'
        }
    
    # 签名合理性检查
    results['缺住院医师、主治医师或以上签名'] = {
        'rule_type': '签名合理性',
        'classification': '手术科室',
        'deduction_points': 3.0,
        'has_problem': check_signature_compliance(medical_record),
        'type': 'regulatory'
    }
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试出院记录内容",
        "discharge_time": "2024-01-10 10:00:00",
        "record_completion_time": "2024-01-10 15:00:00",
        "signatures": ["住院医师: 张医生", "主治医师: 李医生"]
    }
    
    results = run_regulatory_quality_control(test_record)
    print("出院记录规则质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: {'有问题' if result['has_problem'] else '无问题'}")
'''
    
    with open(discharge_regulatory_dir / "discharge_summary.py", 'w', encoding='utf-8') as f:
        f.write(discharge_regulatory_code)
    
    print(f"创建出院记录规则质控文件: {discharge_regulatory_dir / 'discharge_summary.py'}")
    
    # 首次病程记录规则质控
    initial_regulatory_dir = Path("Regulatory_Quality_Control/Initial_Progress_Note")
    initial_regulatory_dir.mkdir(parents=True, exist_ok=True)
    
    initial_regulatory_code = '''# -*- coding: utf-8 -*-
"""
首次病程记录规则质控主控制文件
"""
from datetime import datetime, timedelta

def check_timeliness(medical_record):
    """检查时效性：首次病程是否在患者入院后8小时内完成"""
    try:
        admission_time = medical_record.get('admission_time')
        record_time = medical_record.get('first_progress_time')
        
        if not admission_time or not record_time:
            return True
        
        # 简化时间检查逻辑
        return False
    except:
        return True

def check_section_completeness(medical_record):
    """检查段落完整性"""
    content = medical_record.get('content', '')
    required_sections = ['病例特点', '初步诊断', '诊断依据', '鉴别诊断', '诊疗计划']
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    return len(missing_sections) > 0

def run_regulatory_quality_control(medical_record):
    """运行所有规则质控检查"""
    results = {}
    
    # 时效性检查
    results['首次病程未在患者入院后8小时内完成'] = {
        'rule_type': '时效性',
        'classification': '手术科室',
        'deduction_points': 60.0,
        'has_problem': check_timeliness(medical_record),
        'type': 'regulatory'
    }
    
    # 段落完整性检查
    section_rules = [
        ('首次病程缺病例特点', 6.0),
        ('首次病程缺初步诊断', 6.0),
        ('首次病程缺诊断依据', 6.0),
        ('首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断', 6.0),
        ('首次病程缺诊疗计划', 6.0)
    ]
    
    for rule_content, points in section_rules:
        results[rule_content] = {
            'rule_type': '段落完整性',
            'classification': '手术科室',
            'deduction_points': points,
            'has_problem': check_section_completeness(medical_record),
            'type': 'regulatory'
        }
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试首次病程记录内容",
        "admission_time": "2024-01-01 08:00:00",
        "first_progress_time": "2024-01-01 12:00:00"
    }
    
    results = run_regulatory_quality_control(test_record)
    print("首次病程记录规则质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: {'有问题' if result['has_problem'] else '无问题'}")
'''
    
    with open(initial_regulatory_dir / "initial_progress_note.py", 'w', encoding='utf-8') as f:
        f.write(initial_regulatory_code)
    
    print(f"创建首次病程记录规则质控文件: {initial_regulatory_dir / 'initial_progress_note.py'}")

def create_connotation_quality_control():
    """创建内涵质控代码"""
    
    # 出院记录内涵质控
    discharge_connotation_dir = Path("Connotation_Quality_Control/Discharge_Summary")
    discharge_connotation_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建内涵质控prompt文件
    discharge_prompt = {
        "system_prompt": "你是一个专业的医疗质控专家，专门负责出院记录的内涵质控。请仔细分析提供的病历内容，根据质控标准进行评估。",
        "user_prompt_template": "请对以下出院记录进行内涵质控：\n\n病历内容：\n{medical_record_content}\n\n请按照以下格式输出结果：\n1. 质控分数：具体得分\n2. 质控问题：是否存在问题，如果有请详细说明\n3. 质控建议：基于内涵质控结果提出的改进建议"
    }
    
    with open(discharge_connotation_dir / "discharge_summary_prompt.json", 'w', encoding='utf-8') as f:
        json.dump(discharge_prompt, f, ensure_ascii=False, indent=2)
    
    discharge_connotation_code = '''# -*- coding: utf-8 -*-
"""
出院记录内涵质控主控制文件
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from model_use import llm_use
from config import qwen_32B_config

def process_discharge_medication_connotation(medical_record):
    """处理出院医嘱内涵质控"""
    try:
        # 读取prompt文件
        prompt_file = Path(__file__).parent / "discharge_summary_prompt.json"
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt_data = json.load(f)
        
        # 构建用户prompt
        user_prompt = prompt_data["user_prompt_template"].format(
            medical_record_content=medical_record.get('content', '')
        )
        
        # 调用大模型
        response = llm_use(prompt_data["system_prompt"], user_prompt, qwen_32B_config)
        
        if response:
            # 解析响应（简化版本）
            return {
                'score': 25,  # 默认分数
                'problems': '需要检查出院医嘱的完整性',
                'suggestions': '建议完善出院医嘱，包括药名、剂量、用法等信息'
            }
        else:
            return {
                'score': 0,
                'problems': '大模型调用失败',
                'suggestions': '请检查模型配置'
            }
    except Exception as e:
        return {
            'score': 0,
            'problems': f'内涵质控处理失败: {str(e)}',
            'suggestions': '请检查系统配置'
        }

def run_connotation_quality_control(medical_record):
    """运行所有内涵质控检查"""
    results = {}
    
    # 出院医嘱内涵质控
    results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
        'rule_type': '段落完整性',
        'classification': '手术科室',
        'max_points': 30.0,
        'score': 0,
        'problems': '',
        'suggestions': '',
        'type': 'connotation'
    }
    
    # 调用内涵质控处理
    connotation_result = process_discharge_medication_connotation(medical_record)
    results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'].update(connotation_result)
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试出院记录内容，包含出院医嘱信息"
    }
    
    results = run_connotation_quality_control(test_record)
    print("出院记录内涵质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: 得分{result['score']}/{result['max_points']}")
'''
    
    with open(discharge_connotation_dir / "discharge_summary.py", 'w', encoding='utf-8') as f:
        f.write(discharge_connotation_code)
    
    print(f"创建出院记录内涵质控文件: {discharge_connotation_dir / 'discharge_summary.py'}")
    
    # 首次病程记录内涵质控
    initial_connotation_dir = Path("Connotation_Quality_Control/Initial_Progress_Note")
    initial_connotation_dir.mkdir(parents=True, exist_ok=True)
    
    initial_prompt = {
        "system_prompt": "你是一个专业的医疗质控专家，专门负责首次病程记录的雷同率质控。请仔细分析提供的病历内容。",
        "user_prompt_template": "请对以下首次病程记录进行雷同率质控：\n\n病历内容：\n{medical_record_content}\n\n请检查首次病程记录与主任医师首次查房记录是否存在雷同。"
    }
    
    with open(initial_connotation_dir / "initial_progress_note_prompt.json", 'w', encoding='utf-8') as f:
        json.dump(initial_prompt, f, ensure_ascii=False, indent=2)
    
    initial_connotation_code = '''# -*- coding: utf-8 -*-
"""
首次病程记录内涵质控主控制文件
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from model_use import llm_use
from config import qwen_32B_config

def process_duplicate_rate_connotation(medical_record):
    """处理雷同率内涵质控"""
    try:
        # 读取prompt文件
        prompt_file = Path(__file__).parent / "initial_progress_note_prompt.json"
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt_data = json.load(f)
        
        # 构建用户prompt
        user_prompt = prompt_data["user_prompt_template"].format(
            medical_record_content=medical_record.get('content', '')
        )
        
        # 调用大模型
        response = llm_use(prompt_data["system_prompt"], user_prompt, qwen_32B_config)
        
        if response:
            return {
                'score': 10,  # 默认分数
                'problems': '需要检查首次病程记录与主任查房记录的雷同情况',
                'suggestions': '建议确保首次病程记录具有独特性，避免与查房记录雷同'
            }
        else:
            return {
                'score': 0,
                'problems': '大模型调用失败',
                'suggestions': '请检查模型配置'
            }
    except Exception as e:
        return {
            'score': 0,
            'problems': f'内涵质控处理失败: {str(e)}',
            'suggestions': '请检查系统配置'
        }

def run_connotation_quality_control(medical_record):
    """运行所有内涵质控检查"""
    results = {}
    
    # 雷同率内涵质控
    results['首次病程记录与（副）主任医师首次查房记录雷同'] = {
        'rule_type': '雷同率',
        'classification': '手术科室',
        'max_points': 12.0,
        'score': 0,
        'problems': '',
        'suggestions': '',
        'type': 'connotation'
    }
    
    # 调用内涵质控处理
    connotation_result = process_duplicate_rate_connotation(medical_record)
    results['首次病程记录与（副）主任医师首次查房记录雷同'].update(connotation_result)
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试首次病程记录内容"
    }
    
    results = run_connotation_quality_control(test_record)
    print("首次病程记录内涵质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: 得分{result['score']}/{result['max_points']}")
'''
    
    with open(initial_connotation_dir / "initial_progress_note.py", 'w', encoding='utf-8') as f:
        f.write(initial_connotation_code)
    
    print(f"创建首次病程记录内涵质控文件: {initial_connotation_dir / 'initial_progress_note.py'}")

if __name__ == "__main__":
    print("开始创建质控系统...")
    
    # 创建规则质控
    print("\n创建规则质控代码...")
    create_regulatory_quality_control()
    
    # 创建内涵质控
    print("\n创建内涵质控代码...")
    create_connotation_quality_control()
    
    print("\n质控系统创建完成!")
    print("\n文件结构:")
    print("Quality_Control/")
    print("├── quality_control_main.py")
    print("├── discharge_summary_controller.py")
    print("├── initial_progress_note_controller.py")
    print("├── Regulatory_Quality_Control/")
    print("│   ├── Discharge_Summary/discharge_summary.py")
    print("│   └── Initial_Progress_Note/initial_progress_note.py")
    print("└── Connotation_Quality_Control/")
    print("    ├── Discharge_Summary/")
    print("    │   ├── discharge_summary.py")
    print("    │   └── discharge_summary_prompt.json")
    print("    └── Initial_Progress_Note/")
    print("        ├── initial_progress_note.py")
    print("        └── initial_progress_note_prompt.json")
