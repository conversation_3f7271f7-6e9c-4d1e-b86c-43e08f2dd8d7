# -*- coding: utf-8 -*-
"""
质控代码生成器
根据 JSON 文件生成规则质控和内涵质控代码
"""
import json
import os
import sys
from pathlib import Path

# 添加父目录到路径以导入模块
sys.path.append(str(Path(__file__).parent.parent))
from model_use import llm_use
from config import glm_code_config, qwen_32B_config

class QualityControlGenerator:
    def __init__(self, json_dir="rule_type/rule_type_json"):
        self.json_dir = Path(__file__).parent.parent / json_dir
        self.base_dir = Path(__file__).parent
        
    def load_json_files(self):
        """加载所有 JSON 文件"""
        json_files = {}
        for json_file in self.json_dir.glob("*.json"):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                document_type_english = data['metadata']['document_type_english']
                json_files[document_type_english] = data
        return json_files
    
    def generate_regulatory_quality_control(self, document_type_english, records):
        """生成规则质控代码"""
        regulatory_dir = self.base_dir / "Regulatory_Quality_Control" / document_type_english
        regulatory_dir.mkdir(parents=True, exist_ok=True)
        
        # 过滤规则质控记录
        regulatory_records = [r for r in records if r['type'] in ['规则', '规则和内涵']]
        
        generated_files = []
        
        for i, record in enumerate(regulatory_records):
            # 生成单个质控标准文件
            file_name = f"{record['rule_type_english'].lower().replace(' ', '_')}_{i+1}.py"
            file_path = regulatory_dir / file_name
            
            # 使用大模型生成规则质控代码
            system_prompt = """你是一个专业的医疗质控代码生成专家。请根据提供的质控规则生成Python代码。

要求：
1. 生成的函数应该接收病历数据作为输入
2. 返回二元判断结果（True表示有问题，False表示没有问题）
3. 代码应该包含详细的注释
4. 使用合适的数据结构和算法
5. 考虑各种边界情况和异常处理
6. 函数名格式：check_{rule_type_english}_{index}
"""
            
            user_prompt = f"""请为以下质控规则生成Python代码：

规则类型：{record['rule_type_chinese']} ({record['rule_type_english']})
分类：{record['classification_chinese']} ({record['classification_english']})
所属项目：{record['belonging_project_chinese']} ({record['belonging_project_english']})
文档类型：{record['document_type_chinese']} ({record['document_type_english']})
规则内容：{record['rule_content']}
扣分：{record['deduction_points']}分

请生成一个完整的Python文件，包含：
1. 必要的导入语句
2. 主要的检查函数
3. 辅助函数（如需要）
4. 使用示例
"""
            
            # 使用 qwen_32B_config 生成规则质控代码
            generated_code = llm_use(system_prompt, user_prompt, qwen_32B_config)
            
            if generated_code:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(generated_code)
                generated_files.append(file_name)
                print(f"生成规则质控文件: {file_path}")
        
        # 生成主控制文件
        self._generate_regulatory_main_controller(document_type_english, generated_files, regulatory_records)
        
        return generated_files
    
    def generate_connotation_quality_control(self, document_type_english, records):
        """生成内涵质控代码"""
        connotation_dir = self.base_dir / "Connotation_Quality_Control" / document_type_english
        connotation_dir.mkdir(parents=True, exist_ok=True)
        
        # 过滤内涵质控记录
        connotation_records = [r for r in records if r['type'] in ['内涵', '规则和内涵']]
        
        generated_files = []
        
        for i, record in enumerate(connotation_records):
            # 生成 prompt 文件
            prompt_file_name = f"{record['rule_type_english'].lower().replace(' ', '_')}_{i+1}_prompt.json"
            prompt_file_path = connotation_dir / prompt_file_name
            
            # 生成 system prompt 和 user prompt
            system_prompt_content = f"""你是一个专业的医疗质控专家，专门负责{record['document_type_chinese']}的{record['rule_type_chinese']}质控。

你的任务是：
1. 仔细分析提供的病历内容
2. 根据质控标准进行评估
3. 提供详细的质控结果

质控标准：{record['rule_content']}
扣分标准：{record['deduction_points']}分

请按照以下格式输出结果：
1. 质控分数：具体得分（满分{record['deduction_points']}分）
2. 质控问题：是否存在问题，如果有请详细说明
3. 质控建议：基于内涵质控结果提出的改进建议
"""
            
            user_prompt_template = """请对以下{document_type}进行{rule_type}质控：

病历内容：
{{medical_record_content}}

请严格按照质控标准进行评估，并提供详细的分析结果。"""
            
            prompt_data = {
                "system_prompt": system_prompt_content,
                "user_prompt_template": user_prompt_template.format(
                    document_type=record['document_type_chinese'],
                    rule_type=record['rule_type_chinese']
                ),
                "rule_info": {
                    "rule_type_chinese": record['rule_type_chinese'],
                    "rule_type_english": record['rule_type_english'],
                    "classification_chinese": record['classification_chinese'],
                    "classification_english": record['classification_english'],
                    "belonging_project_chinese": record['belonging_project_chinese'],
                    "belonging_project_english": record['belonging_project_english'],
                    "document_type_chinese": record['document_type_chinese'],
                    "document_type_english": record['document_type_english'],
                    "rule_content": record['rule_content'],
                    "deduction_points": record['deduction_points']
                }
            }
            
            with open(prompt_file_path, 'w', encoding='utf-8') as f:
                json.dump(prompt_data, f, ensure_ascii=False, indent=2)
            
            # 生成处理代码文件
            code_file_name = f"{record['rule_type_english'].lower().replace(' ', '_')}_{i+1}.py"
            code_file_path = connotation_dir / code_file_name
            
            # 使用大模型生成内涵质控处理代码
            code_system_prompt = """你是一个专业的医疗质控代码生成专家。请生成内涵质控处理代码。

要求：
1. 代码应该读取对应的 prompt 文件
2. 调用大模型进行内涵质控分析
3. 解析大模型返回的结果
4. 返回结构化的质控结果
5. 包含异常处理和错误处理
6. 函数名格式：process_{rule_type_english}_{index}
"""
            
            code_user_prompt = f"""请为以下内涵质控生成Python处理代码：

规则类型：{record['rule_type_chinese']} ({record['rule_type_english']})
Prompt文件：{prompt_file_name}

生成的代码应该：
1. 读取 {prompt_file_name} 文件
2. 使用 qwen_32B_config 调用大模型
3. 解析返回结果，提取质控分数、质控问题、质控建议
4. 返回结构化的结果字典

请生成完整的Python文件。
"""
            
            # 使用 glm_code_config 生成内涵质控代码
            generated_code = llm_use(code_system_prompt, code_user_prompt, glm_code_config)
            
            if generated_code:
                with open(code_file_path, 'w', encoding='utf-8') as f:
                    f.write(generated_code)
                generated_files.append((code_file_name, prompt_file_name))
                print(f"生成内涵质控文件: {code_file_path}")
                print(f"生成内涵质控Prompt文件: {prompt_file_path}")
        
        # 生成主控制文件
        self._generate_connotation_main_controller(document_type_english, generated_files, connotation_records)

        return generated_files

    def _generate_regulatory_main_controller(self, document_type_english, generated_files, records):
        """生成规则质控主控制文件"""
        main_file_path = self.base_dir / "Regulatory_Quality_Control" / document_type_english / f"{document_type_english.lower()}.py"

        imports = []
        function_calls = []

        for i, (file_name, record) in enumerate(zip(generated_files, records)):
            module_name = file_name.replace('.py', '')
            function_name = f"check_{record['rule_type_english'].lower().replace(' ', '_')}_{i+1}"
            imports.append(f"from .{module_name} import {function_name}")
            function_calls.append(f"        result = {function_name}(medical_record)")
            function_calls.append(f"        results['{record['rule_content']}'] = {{")
            function_calls.append(f"            'rule_type': '{record['rule_type_chinese']}',")
            function_calls.append(f"            'classification': '{record['classification_chinese']}',")
            function_calls.append(f"            'deduction_points': {record['deduction_points']},")
            function_calls.append(f"            'has_problem': result,")
            function_calls.append(f"            'type': 'regulatory'")
            function_calls.append(f"        }}")
            function_calls.append("")

        main_content = f'''# -*- coding: utf-8 -*-
"""
{document_type_english} 规则质控主控制文件
"""
{chr(10).join(imports)}

def run_regulatory_quality_control(medical_record):
    """
    运行所有规则质控检查

    Args:
        medical_record: 病历数据

    Returns:
        dict: 规则质控结果
    """
    results = {{}}

{chr(10).join(function_calls)}
    return results

if __name__ == "__main__":
    # 测试示例
    test_record = {{
        "content": "测试病历内容",
        "patient_info": {{}},
        "diagnosis": {{}},
        "treatment": {{}}
    }}

    results = run_regulatory_quality_control(test_record)
    print("规则质控结果:")
    for rule, result in results.items():
        print(f"- {{rule}}: {{result}}")
'''

        with open(main_file_path, 'w', encoding='utf-8') as f:
            f.write(main_content)

        print(f"生成规则质控主控制文件: {main_file_path}")

    def _generate_connotation_main_controller(self, document_type_english, generated_files, records):
        """生成内涵质控主控制文件"""
        main_file_path = self.base_dir / "Connotation_Quality_Control" / document_type_english / f"{document_type_english.lower()}.py"

        imports = []
        function_calls = []

        for i, ((code_file, prompt_file), record) in enumerate(zip(generated_files, records)):
            module_name = code_file.replace('.py', '')
            function_name = f"process_{record['rule_type_english'].lower().replace(' ', '_')}_{i+1}"
            imports.append(f"from .{module_name} import {function_name}")
            function_calls.append(f"        result = {function_name}(medical_record)")
            function_calls.append(f"        results['{record['rule_content']}'] = {{")
            function_calls.append(f"            'rule_type': '{record['rule_type_chinese']}',")
            function_calls.append(f"            'classification': '{record['classification_chinese']}',")
            function_calls.append(f"            'max_points': {record['deduction_points']},")
            function_calls.append(f"            'score': result.get('score', 0),")
            function_calls.append(f"            'problems': result.get('problems', ''),")
            function_calls.append(f"            'suggestions': result.get('suggestions', ''),")
            function_calls.append(f"            'type': 'connotation'")
            function_calls.append(f"        }}")
            function_calls.append("")

        main_content = f'''# -*- coding: utf-8 -*-
"""
{document_type_english} 内涵质控主控制文件
"""
{chr(10).join(imports)}

def run_connotation_quality_control(medical_record):
    """
    运行所有内涵质控检查

    Args:
        medical_record: 病历数据

    Returns:
        dict: 内涵质控结果
    """
    results = {{}}

{chr(10).join(function_calls)}
    return results

if __name__ == "__main__":
    # 测试示例
    test_record = {{
        "content": "测试病历内容",
        "patient_info": {{}},
        "diagnosis": {{}},
        "treatment": {{}}
    }}

    results = run_connotation_quality_control(test_record)
    print("内涵质控结果:")
    for rule, result in results.items():
        print(f"- {{rule}}: {{result}}")
'''

        with open(main_file_path, 'w', encoding='utf-8') as f:
            f.write(main_content)

        print(f"生成内涵质控主控制文件: {main_file_path}")

    def generate_all(self):
        """生成所有质控代码"""
        json_files = self.load_json_files()

        for document_type_english, data in json_files.items():
            print(f"\n开始生成 {document_type_english} 的质控代码...")

            records = data['records']

            # 生成规则质控代码
            print(f"生成规则质控代码...")
            self.generate_regulatory_quality_control(document_type_english, records)

            # 生成内涵质控代码
            print(f"生成内涵质控代码...")
            self.generate_connotation_quality_control(document_type_english, records)

            print(f"{document_type_english} 质控代码生成完成!")

if __name__ == "__main__":
    generator = QualityControlGenerator()
    generator.generate_all()
