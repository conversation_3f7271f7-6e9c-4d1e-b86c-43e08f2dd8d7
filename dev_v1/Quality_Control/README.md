# 医疗病历质控系统 (Quality Control System)

## 系统概述

医疗病历质控系统是一个综合性的病历质量控制平台，结合了基于规则的自动化检查和基于LLM的智能分析，实现对医疗病历的全面质量评估。

## 系统架构

```
Quality_Control/
├── Connotation_Quality_Control/     # 内涵质控模块
│   ├── __init__.py                  # 模块初始化
│   ├── connotation_processor.py     # 内涵质控处理器
│   ├── llm_quality_checker.py       # LLM质控检查器
│   ├── quality_evaluator.py         # 质量评估器
│   └── README.md                    # 内涵质控说明
├── Regulatory_Quality_Control/      # 规则质控模块
│   ├── __init__.py                  # 模块初始化
│   ├── rule_code_generator.py       # 规则代码生成器
│   ├── regulatory_processor.py      # 规则处理器
│   └── README.md                    # 规则质控说明
├── tests/                           # 测试文件目录
│   ├── __init__.py                  # 测试模块初始化
│   ├── test_connotation_quality_control.py  # 内涵质控测试
│   ├── test_regulatory_quality_control.py   # 规则质控测试
│   └── test_integration.py          # 集成测试
└── README.md                        # 本文档
```

## 核心功能

### 1. 内涵质控 (Connotation Quality Control)
基于大语言模型(LLM)的智能质控，专注于需要医学专业判断的复杂质控项目：

- **诊断逻辑性校验**：评估诊断演进的合理性和一致性
- **临床信息质量校验**：检查症状、检查结果、会诊记录的完整性和准确性
- **治疗过程校验**：评估病程记录、治疗效果、并发症处理的合理性
- **跨要素一致性校验**：检查各部分内容的内在逻辑一致性

### 2. 规则质控 (Regulatory Quality Control)
基于代码规则的自动化质控，专注于结构化、可编程的质控检查：

- **基础信息完整性校验**：检查必填字段的完整性
- **时效性校验**：验证文档完成时间和时间逻辑
- **格式规范性校验**：检查文档格式和结构规范
- **特殊标记校验**：验证肿瘤标记等特殊标识的一致性

## 质控流程

### 整体流程图

```mermaid
graph TD
    A[输入病历数据] --> B[数据预处理]
    B --> C{质控类型选择}
    C -->|规则质控| D[Regulatory Quality Control]
    C -->|内涵质控| E[Connotation Quality Control]
    C -->|综合质控| F[并行执行两种质控]
    
    D --> G[基础信息检查]
    D --> H[时效性检查]
    D --> I[格式规范检查]
    D --> J[特殊标记检查]
    
    E --> K[诊断逻辑性检查]
    E --> L[临床信息质量检查]
    E --> M[治疗过程检查]
    E --> N[一致性检查]
    
    G --> O[规则质控结果]
    H --> O
    I --> O
    J --> O
    
    K --> P[内涵质控结果]
    L --> P
    M --> P
    N --> P
    
    F --> Q[结果合并]
    O --> Q
    P --> Q
    Q --> R[生成质控报告]
    R --> S[输出最终结果]
```

### 详细执行步骤

1. **数据输入与预处理**
   - 接收病历数据（JSON格式）
   - 验证数据结构完整性
   - 提取关键信息字段

2. **规则质控执行**
   - 基础信息完整性校验（15%权重）
   - 特殊标记校验（5%权重）
   - 时效性和格式检查

3. **内涵质控执行**
   - 诊断逻辑性校验（20%权重）
   - 临床信息质量校验（20%权重）
   - 治疗过程校验（20%权重）
   - 出院管理校验（15%权重）
   - 跨要素一致性校验（5%权重）

4. **结果整合与评分**
   - 计算各项目得分
   - 应用权重计算总分
   - 确定整体质控状态

5. **报告生成**
   - 生成详细质控报告
   - 提供改进建议
   - 输出标准化结果

## 评分体系

### 权重分配
| 质控项目 | 权重 | 质控类型 | 备注 |
|---------|------|----------|------|
| 基础信息完整性 | 15% | 规则质控 | 必填字段检查 |
| 诊断逻辑性 | 20% | 内涵质控 | 诊断演进合理性 |
| 临床信息质量 | 20% | 内涵质控 | 症状、检查、会诊记录 |
| 治疗过程 | 20% | 内涵质控 | 病程、疗效、并发症 |
| 出院管理 | 15% | 内涵质控 | 出院情况、用药建议 |
| 肿瘤标记一致性 | 5% | 规则质控 | 肿瘤标记与诊断一致性 |
| 跨要素一致性 | 5% | 内涵质控 | 各部分内在逻辑一致性 |

### 评分标准
- **90-100分**：质量优良，状态为"pass"
- **70-89分**：存在轻微问题，状态为"warning"
- **0-69分**：存在严重问题，状态为"error"

## 使用方法

### 基本使用示例

```python
from Quality_Control.connotation_processor import ConnotationProcessor
from Quality_Control.regulatory_processor import RegulatoryProcessor

# 创建质控处理器
connotation_processor = ConnotationProcessor()
regulatory_processor = RegulatoryProcessor()

# 执行质控检查
patient_info = {...}  # 患者信息
medical_record = {...}  # 病历数据

# 内涵质控
connotation_result = connotation_processor.check_quality(patient_info, medical_record)

# 规则质控
regulatory_result = regulatory_processor.check_quality(patient_info, medical_record)

# 合并结果
final_result = merge_quality_results(connotation_result, regulatory_result)
```

### 并发执行示例

```python
import asyncio
from Quality_Control import QualityControlSystem

# 创建质控系统
qc_system = QualityControlSystem()

# 并发执行质控
async def run_quality_control():
    result = await qc_system.check_quality_async(patient_info, medical_record)
    return result

# 执行
result = asyncio.run(run_quality_control())
```

## 输出格式

### 标准输出格式

```json
{
  "overall_score": 85.5,
  "overall_status": "warning",
  "total_deduction": 14.5,
  "timestamp": "2025-01-17T10:30:00Z",
  "quality_control_results": {
    "regulatory": {
      "score": 90.0,
      "status": "pass",
      "checks": [...]
    },
    "connotation": {
      "score": 82.0,
      "status": "warning",
      "checks": [...]
    }
  },
  "elements": [
    {
      "element": "基础信息完整性",
      "score": 100,
      "status": "pass",
      "weight": 0.15,
      "suggestions": []
    }
  ],
  "suggestions": [
    {
      "type": "问题类型代码",
      "message": "具体问题描述",
      "severity": "严重程度",
      "category": "问题分类",
      "elements": ["相关要素1", "相关要素2"]
    }
  ]
}
```

## 配置说明

### 模型配置
系统支持多种LLM模型配置，在`config.py`中定义：
- Qwen系列模型
- Kimi模型
- DeepSeek模型
- GLM模型

### 质控规则配置
- 规则质控：通过JSON文件定义规则
- 内涵质控：通过提示词模板配置LLM检查逻辑

## 测试说明

### 测试覆盖范围
- **单元测试**：各个模块的独立功能测试
- **集成测试**：模块间协作功能测试
- **性能测试**：并发处理能力测试
- **准确性测试**：质控结果准确性验证

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_connotation_quality_control.py
python -m pytest tests/test_regulatory_quality_control.py
python -m pytest tests/test_integration.py
```

## 扩展开发

### 添加新的质控规则
1. **规则质控**：在相应的JSON文件中添加新规则
2. **内涵质控**：在提示词模板中添加新的检查逻辑

### 自定义评分权重
修改权重配置，调整各质控项目的重要性

### 集成新的LLM模型
在`config.py`中添加新的模型配置

## 版本信息

- **当前版本**：1.0.0
- **最后更新**：2025-01-17
- **兼容性**：Python 3.8+

## 联系信息

如有问题或建议，请联系开发团队。
